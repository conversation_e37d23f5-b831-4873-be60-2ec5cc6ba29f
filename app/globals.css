@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .bg-primary-dark {
    @apply bg-primary/90;
  }
  .container-custom {
    @apply mx-auto px-4;

    /* Default for mobile (up to 640px) */
    width: 100%;
    max-width: 100%;

    /* SM breakpoint (640px) */
    @media (min-width: 640px) {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }

    /* MD breakpoint (768px) */
    @media (min-width: 768px) {
      padding-left: 2rem;
      padding-right: 2rem;
    }

    /* LG breakpoint (1024px) */
    @media (min-width: 1024px) {
      padding-left: 2.5rem;
      padding-right: 2.5rem;
    }

    /* XL breakpoint (1280px) */
    @media (min-width: 1280px) {
      width: 85%;
      max-width: 1400px;
    }

    /* 2XL breakpoint (1536px) */
    @media (min-width: 1536px) {
      width: 1400px;
      max-width: 1400px;
    }
  }
}

@layer base {
  :root {
    --background: 240 13% 98%;  /* #f8f8fb */
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 228 78% 62%;  /* #556ee6 */
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 228 78% 62%;  /* #556ee6 */
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 228 78% 62%;  /* #556ee6 */
    --primary-foreground: 0 0% 98%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 228 78% 62%;  /* #556ee6 */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-poppins), sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  html {
    font-size: 14px; /* This would explicitly set the base font size */
  }
}

@layer base {
  /* Remove or comment out the previous button styles */

  /* Add new specific styles for the desktop menu */
  .desktop-nav-button {
    color: #fff9; /* Default slightly transparent white */
    transition: color 0.2s ease-in-out;
  }

  .desktop-nav-button:hover,
  .desktop-nav-button[aria-expanded="true"] {
    color: #ffff; /* Full white on hover/active */
  }

  /* Fix for PrimeReact search input with icon */
  .p-input-icon-left > .p-inputtext {
    padding-left: 2.5rem !important;
  }

  .p-input-icon-left > i {
    left: 0.75rem;
    color: #9ca3af;
    z-index: 10;
  }

  /* Ensure proper spacing for custom search inputs */
  .search-input-container {
    position: relative;
  }

  .search-input-container .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    z-index: 10;
    pointer-events: none;
  }

  .search-input-container input {
    padding-left: 2.5rem !important;
  }
}













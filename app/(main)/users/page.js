"use client";
import React, { useState } from "react";
import { But<PERSON> } from "primereact/button";

import DataTable from "@/components/ui/data-table";
import { useTableData } from "@/hooks/useTableData";
import {
  createTextColumn,
  createDateColumn,
  createTagColumn,
  createBooleanColumn,
  createImageColumn,
  createActionColumn,
  statusConfigurations,
} from "@/utils/tableColumns";

const UsersPage = () => {
  const [selectedUser, setSelectedUser] = useState(null);

  // Generate mock data once
  const [initialUsers] = useState(() => {
    const firstNames = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];
    const lastNames = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];
    const domains = ["gmail.com", "yahoo.com", "outlook.com", "company.com"];

    return Array.from({ length: 25 }, (_, i) => {
      const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
      const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
      const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${domains[Math.floor(Math.random() * domains.length)]}`;

      return {
        id: i + 1,
        name: `${firstName} ${lastName}`,
        email,
        phone: `+1 (${Math.floor(Math.random() * 900) + 100}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
        status: statusConfigurations.user[Math.floor(Math.random() * statusConfigurations.user.length)].value,
        isActive: Math.random() > 0.3,
        createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
        avatar: `https://i.pravatar.cc/40?img=${i + 1}`,
        role: ["Admin", "User", "Manager", "Editor"][Math.floor(Math.random() * 4)],
      };
    });
  });

  // Use the table data hook
  const { data: users, loading, updateItem, deleteItem } = useTableData(initialUsers);

  // Handle row click
  const handleRowClick = (event) => {
    setSelectedUser(event.data);
    console.log("Selected user:", event.data);
  };

  // Handle user actions
  const handleEdit = (user) => {
    console.log("Edit user:", user);
    // Open edit dialog
  };

  const handleDelete = (user) => {
    if (confirm(`Are you sure you want to delete ${user.name}?`)) {
      deleteItem(user.id);
    }
  };

  const handleToggleStatus = (user) => {
    const newStatus = user.status === "Active" ? "Inactive" : "Active";
    updateItem({ ...user, status: newStatus });
  };

  // Action buttons template
  const actionBodyTemplate = (rowData) => {
    return (
      <div className="flex gap-2">
        <Button
          icon="pi pi-pencil"
          className="p-button-rounded p-button-text p-button-sm"
          onClick={(e) => {
            e.stopPropagation();
            handleEdit(rowData);
          }}
          tooltip="Edit"
        />
        <Button
          icon="pi pi-refresh"
          className="p-button-rounded p-button-text p-button-sm"
          onClick={(e) => {
            e.stopPropagation();
            handleToggleStatus(rowData);
          }}
          tooltip="Toggle Status"
        />
        <Button
          icon="pi pi-trash"
          className="p-button-rounded p-button-text p-button-sm p-button-danger"
          onClick={(e) => {
            e.stopPropagation();
            handleDelete(rowData);
          }}
          tooltip="Delete"
        />
      </div>
    );
  };

  // Define table columns
  const columns = [
    createImageColumn("avatar", "Avatar", {
      style: { minWidth: "6rem" },
    }),
    createTextColumn("name", "Name", {
      style: { minWidth: "12rem" },
    }),
    createTextColumn("email", "Email", {
      style: { minWidth: "15rem" },
    }),
    createTextColumn("phone", "Phone", {
      style: { minWidth: "12rem" },
    }),
    createTextColumn("role", "Role", {
      filterType: "dropdown",
      filterOptions: ["Admin", "User", "Manager", "Editor"],
      style: { minWidth: "8rem" },
    }),
    createTagColumn("status", "Status", statusConfigurations.user, {
      style: { minWidth: "8rem" },
    }),
    createBooleanColumn("isActive", "Active", {
      style: { minWidth: "6rem" },
    }),
    createDateColumn("createdAt", "Created", {
      style: { minWidth: "10rem" },
    }),
    createActionColumn("Actions", actionBodyTemplate, {
      style: { minWidth: "10rem" },
    }),
  ];

  // Table actions
  const tableActions = (
    <>
      <Button
        label="Add User"
        icon="pi pi-plus"
        className="p-button-sm"
        onClick={() => console.log("Add new user")}
      />
      <Button
        label="Import"
        icon="pi pi-upload"
        className="p-button-sm p-button-outlined"
        onClick={() => console.log("Import users")}
      />
    </>
  );

  return (
    <div className="space-y-6">
      <DataTable
        data={users}
        columns={columns}
        loading={loading}
        title="Users Management"
        searchPlaceholder="Search users..."
        emptyMessage="No users found."
        onRowClick={handleRowClick}
        globalFilterFields={["name", "email", "phone", "role"]}
        exportable={true}
        onExport={() => console.log("Export users")}
        actions={tableActions}
        rows={15}
        rowsPerPageOptions={[10, 15, 25, 50]}
      />

      {selectedUser && (
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold mb-2">Selected User</h3>
          <pre className="text-sm bg-gray-100 p-3 rounded">
            {JSON.stringify(selectedUser, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default UsersPage;

"use client";
import React, { useState } from "react";

import DataTable from "@/components/ui/data-table";
import ProductEditForm from "@/components/features/ProductEditForm";
import { useTableData } from "@/hooks/useTableData";
import {
  createTextColumn,
  createCurrencyColumn,
  createTagColumn,
  createNumberColumn,
  statusConfigurations,
  categoryConfigurations,
} from "@/utils/tableColumns";

const Dashboard = () => {
  // Dialog state
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState("edit"); // "edit" or "view"

  // Mock data generator
  const generateMockData = () => {
    return Array.from({ length: 30 }, (_, i) => ({
      id: i + 1,
      name: `Product ${i + 1}`,
      price: Math.floor(Math.random() * 1000) + 10,
      category: categoryConfigurations.product[
        Math.floor(Math.random() * categoryConfigurations.product.length)
      ],
      status: statusConfigurations.product[
        Math.floor(Math.random() * statusConfigurations.product.length)
      ].value,
      rating: Math.floor(Math.random() * 5) + 1,
    }));
  };

  // Use the table data hook
  const { data: products, loading, updateItem } = useTableData(generateMockData());

  // Handle row click
  const handleRowClick = (event) => {
    setSelectedProduct(event.data);
    setDialogMode("edit");
    setIsDialogOpen(true);
  };

  // Handle product save
  const handleProductSave = (updatedProduct) => {
    updateItem(updatedProduct);
  };

  // Handle dialog close
  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedProduct(null);
  };

  // Define table columns
  const columns = [
    createTextColumn("name", "Name", {
      filterPlaceholder: "Search by name",
      style: { minWidth: "12rem" },
    }),
    createCurrencyColumn("price", "Price", {
      style: { minWidth: "8rem" },
    }),
    createTagColumn("category", "Category", categoryConfigurations.product.map(cat => ({ value: cat, label: cat })), {
      filterType: "dropdown",
      filterOptions: categoryConfigurations.product,
      style: { minWidth: "12rem" },
    }),
    createTagColumn("status", "Status", statusConfigurations.product, {
      style: { minWidth: "12rem" },
    }),
    createNumberColumn("rating", "Rating", {
      style: { minWidth: "8rem" },
    }),
  ];

  return (
    <>
      <DataTable
        data={products}
        columns={columns}
        loading={loading}
        title="Products"
        searchPlaceholder="Search products..."
        emptyMessage="No products found."
        onRowClick={handleRowClick}
        globalFilterFields={["name", "category", "status"]}
        exportable={true}
        onExport={() => console.log("Export functionality")}
      />

    {/* Product Edit Dialog */}
    <ProductEditForm
      product={selectedProduct}
      isOpen={isDialogOpen}
      onClose={handleDialogClose}
      onSave={handleProductSave}
      mode={dialogMode}
    />
  </>
  );
};

export default Dashboard;

"use client";
import React, { useState } from "react";

import DataTable from "@/components/ui/data-table";
import ProductEditForm from "@/components/features/ProductEditForm";
import ClientOnly from "@/components/ui/client-only";
import TableSkeleton from "@/components/ui/table-skeleton";
import { useTableData } from "@/hooks/useTableData";
import {
  createTextColumn,
  createCurrencyColumn,
  createTagColumn,
  createNumberColumn,
  statusConfigurations,
  categoryConfigurations,
} from "@/utils/tableColumns";

const Dashboard = () => {
  // Dialog state
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState("edit"); // "edit" or "view"

  // Generate deterministic mock data to avoid hydration issues
  const [initialData] = useState(() => {
    return Array.from({ length: 30 }, (_, i) => {
      // Use index-based deterministic values instead of Math.random()
      const priceBase = ((i * 37) % 990) + 10; // Deterministic price
      const categoryIndex = (i * 7) % categoryConfigurations.product.length;
      const statusIndex = (i * 3) % statusConfigurations.product.length;
      const rating = ((i * 5) % 5) + 1;

      return {
        id: i + 1,
        name: `Product ${i + 1}`,
        price: priceBase,
        category: categoryConfigurations.product[categoryIndex],
        status: statusConfigurations.product[statusIndex].value,
        rating: rating,
      };
    });
  });

  // Use the table data hook
  const { data: products, loading, updateItem } = useTableData(initialData);

  // Handle row click
  const handleRowClick = (event) => {
    setSelectedProduct(event.data);
    setDialogMode("edit");
    setIsDialogOpen(true);
  };

  // Handle product save
  const handleProductSave = (updatedProduct) => {
    updateItem(updatedProduct);
  };

  // Handle dialog close
  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedProduct(null);
  };

  // Define table columns
  const columns = [
    createTextColumn("name", "Name", {
      filterPlaceholder: "Search by name",
      style: { minWidth: "12rem" },
    }),
    createCurrencyColumn("price", "Price", {
      style: { minWidth: "8rem" },
    }),
    createTagColumn("category", "Category", categoryConfigurations.product.map(cat => ({ value: cat, label: cat })), {
      filterType: "dropdown",
      filterOptions: categoryConfigurations.product,
      style: { minWidth: "12rem" },
    }),
    createTagColumn("status", "Status", statusConfigurations.product, {
      style: { minWidth: "12rem" },
    }),
    createNumberColumn("rating", "Rating", {
      style: { minWidth: "8rem" },
    }),
  ];

  return (
    <>
      <ClientOnly fallback={<TableSkeleton />}>
        <DataTable
          data={products}
          columns={columns}
          loading={loading}
          title="Products"
          searchPlaceholder="Search products..."
          emptyMessage="No products found."
          onRowClick={handleRowClick}
          globalFilterFields={["name", "category", "status"]}
          exportable={true}
          onExport={() => console.log("Export functionality")}
        />
      </ClientOnly>

    {/* Product Edit Dialog */}
    <ProductEditForm
      product={selectedProduct}
      isOpen={isDialogOpen}
      onClose={handleDialogClose}
      onSave={handleProductSave}
      mode={dialogMode}
    />
  </>
  );
};

export default Dashboard;

"use client";
import React, { useState } from "react";
import { But<PERSON> } from "primereact/button";

import DataTable from "@/components/ui/data-table";
import { useTableData } from "@/hooks/useTableData";
import {
  createTextColumn,
  createCurrencyColumn,
  createDateColumn,
  createTagColumn,
  createNumberColumn,
  createCustomColumn,
  statusConfigurations,
} from "@/utils/tableColumns";

const OrdersPage = () => {
  const [selectedOrder, setSelectedOrder] = useState(null);

  // Generate mock data once
  const [initialOrders] = useState(() => {
    const customers = [
      "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
      "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
    ];

    return Array.from({ length: 40 }, (_, i) => {
      const orderDate = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000);
      const items = Math.floor(Math.random() * 5) + 1;
      const itemPrice = Math.random() * 100 + 10;
      const total = items * itemPrice;

      return {
        id: `ORD-${String(i + 1).padStart(4, '0')}`,
        orderNumber: `#${String(i + 1001).padStart(6, '0')}`,
        customer: customers[Math.floor(Math.random() * customers.length)],
        items,
        total,
        status: statusConfigurations.order[Math.floor(Math.random() * statusConfigurations.order.length)].value,
        orderDate: orderDate.toISOString(),
        shippingAddress: `${Math.floor(Math.random() * 9999) + 1} Main St, City, State ${Math.floor(Math.random() * 90000) + 10000}`,
        paymentMethod: ["Credit Card", "PayPal", "Bank Transfer", "Cash"][Math.floor(Math.random() * 4)],
        discount: Math.random() > 0.7 ? Math.random() * 20 : 0,
      };
    });
  });

  // Use the table data hook
  const { data: orders, loading, updateItem } = useTableData(initialOrders);

  // Handle row click
  const handleRowClick = (event) => {
    setSelectedOrder(event.data);
    console.log("Selected order:", event.data);
  };

  // Custom column templates
  const orderNumberTemplate = (rowData) => (
    <span className="font-mono text-blue-600 font-semibold">
      {rowData.orderNumber}
    </span>
  );

  const itemsTemplate = (rowData) => (
    <span className="bg-gray-100 px-2 py-1 rounded text-sm">
      {rowData.items} item{rowData.items !== 1 ? 's' : ''}
    </span>
  );

  const discountTemplate = (rowData) => {
    if (rowData.discount > 0) {
      return (
        <span className="text-green-600 font-semibold">
          -{rowData.discount.toFixed(1)}%
        </span>
      );
    }
    return <span className="text-gray-400">-</span>;
  };

  // Define table columns
  const columns = [
    createCustomColumn("orderNumber", "Order #", orderNumberTemplate, {
      sortable: true,
      filter: true,
      style: { minWidth: "10rem" },
    }),
    createTextColumn("customer", "Customer", {
      style: { minWidth: "12rem" },
    }),
    createCustomColumn("items", "Items", itemsTemplate, {
      sortable: true,
      style: { minWidth: "8rem" },
    }),
    createCurrencyColumn("total", "Total", {
      style: { minWidth: "10rem" },
    }),
    createTagColumn("status", "Status", statusConfigurations.order, {
      style: { minWidth: "10rem" },
    }),
    createDateColumn("orderDate", "Order Date", {
      format: { year: "numeric", month: "short", day: "numeric" },
      style: { minWidth: "10rem" },
    }),
    createTextColumn("paymentMethod", "Payment", {
      filterType: "dropdown",
      filterOptions: ["Credit Card", "PayPal", "Bank Transfer", "Cash"],
      style: { minWidth: "10rem" },
    }),
    createCustomColumn("discount", "Discount", discountTemplate, {
      sortable: true,
      style: { minWidth: "8rem" },
    }),
  ];

  // Table actions
  const tableActions = (
    <>
      <Button
        label="New Order"
        icon="pi pi-plus"
        className="p-button-sm"
        onClick={() => console.log("Create new order")}
      />
      <Button
        label="Bulk Actions"
        icon="pi pi-cog"
        className="p-button-sm p-button-outlined"
        onClick={() => console.log("Bulk actions")}
      />
    </>
  );

  return (
    <div className="space-y-6">
      <DataTable
        data={orders}
        columns={columns}
        loading={loading}
        title="Orders Management"
        searchPlaceholder="Search orders..."
        emptyMessage="No orders found."
        onRowClick={handleRowClick}
        globalFilterFields={["orderNumber", "customer", "paymentMethod"]}
        exportable={true}
        onExport={() => console.log("Export orders")}
        actions={tableActions}
        rows={20}
        rowsPerPageOptions={[10, 20, 50, 100]}
        sortMode="multiple"
        stripedRows={true}
      />

      {selectedOrder && (
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold mb-4">Order Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p><strong>Order Number:</strong> {selectedOrder.orderNumber}</p>
              <p><strong>Customer:</strong> {selectedOrder.customer}</p>
              <p><strong>Items:</strong> {selectedOrder.items}</p>
              <p><strong>Total:</strong> ${selectedOrder.total.toFixed(2)}</p>
            </div>
            <div>
              <p><strong>Status:</strong> {selectedOrder.status}</p>
              <p><strong>Payment:</strong> {selectedOrder.paymentMethod}</p>
              <p><strong>Discount:</strong> {selectedOrder.discount.toFixed(1)}%</p>
              <p><strong>Order Date:</strong> {new Date(selectedOrder.orderDate).toLocaleDateString()}</p>
            </div>
          </div>
          <div className="mt-4">
            <p><strong>Shipping Address:</strong></p>
            <p className="text-gray-600">{selectedOrder.shippingAddress}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrdersPage;
